import { Request, Response, NextFunction } from 'express'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'

const getPing = async (req: Request, res: Response, next: NextFunction) => {
  try {
    return res.status(200).send('pong')
  } catch (error) {
    next(error)
  }
}

const getHealthCheck = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now()
    const healthStatus: any = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
      environment: process.env.NODE_ENV || 'development'
    }

    try {
      const app = getRunningExpressApp()
      if (app && app.AppDataSource) {
        await app.AppDataSource.query('SELECT 1')
        healthStatus.database = { status: 'connected' }
      } else {
        healthStatus.database = { status: 'not_initialized' }
      }
    } catch (dbError) {
      healthStatus.database = {
        status: 'error',
        error: dbError instanceof Error ? dbError.message : 'Unknown error'
      }
      healthStatus.status = 'degraded'
    }

    healthStatus.responseTime = Date.now() - startTime

    const statusCode = healthStatus.status === 'ok' ? 200 : 503
    return res.status(statusCode).json(healthStatus)
  } catch (error) {
    next(error)
  }
}

export default {
  getPing,
  getHealthCheck
}
