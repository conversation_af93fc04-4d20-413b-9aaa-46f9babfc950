import { MeiliSearch, Setting<PERSON>, type SearchParams } from 'meilisearch'
import { BedrockRuntimeClient, InvokeModelCommand } from '@aws-sdk/client-bedrock-runtime'

// Initialize MeiliSearch client
const client = new MeiliSearch({
  host: process.env.MEILISEARCH_HOST!,
  apiKey: process.env.MEILISEARCH_API_KEY!
})

// Initialize AWS Bedrock client for embedding generation
const bedrockClient = new BedrockRuntimeClient({
  region: process.env.S3_STORAGE_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.S3_STORAGE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.S3_STORAGE_SECRET_ACCESS_KEY || ''
  }
})

// Function to generate embeddings using AWS Bedrock
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const command = new InvokeModelCommand({
      modelId: 'cohere.embed-multilingual-v3',
      contentType: 'application/json',
      accept: 'application/json',
      body: JSON.stringify({
        texts: [text],
        input_type: 'search_query',
        truncate: 'END'
      })
    })

    const response = await bedrockClient.send(command)
    const responseBody = JSON.parse(new TextDecoder().decode(response.body))

    return responseBody.embeddings[0]
  } catch (error) {
    console.error('Error generating embedding:', error)
    throw error
  }
}

// Function to create an index in MeiliSearch
export async function createIndex(indexUid: string) {
  return await client.createIndex(indexUid, { primaryKey: 'id' })
}

// Function to check if a question already exists in the index
export async function checkIfQuestionExists(indexUid: string, question: string) {
  const searchParams: SearchParams = {
    filter: `question = "${question}"`
  }
  const searchResult = await client.index(indexUid).search('', searchParams)
  return searchResult.hits.length > 0 ? null : searchResult
}

// Function to delete an index
export async function deleteIndex(indexUid: string) {
  return await client.index(indexUid).delete()
}

// Function to get task UID
export async function getTaskUid(taskUid: number) {
  return await client.getTask(taskUid)
}

// Function to delete specific documents from the index
export async function deleteDocuments(indexUid: string, documentIds: string[]) {
  return await client.index(indexUid).deleteDocuments(documentIds)
}

// Function to delete all documents from an index
export async function deleteAllDocuments(indexUid: string) {
  return await client.index(indexUid).deleteAllDocuments()
}

// Function to update a document with a vector if not present
export async function updateDocument(indexUid: string, document: Record<string, any>) {
  if (!document.vector && document.question) {
    const vector = await generateEmbedding(document.question)
    document._vectors = {
      default: {
        embeddings: vector,
        regenerate: false
      }
    }
  }

  return await client.index(indexUid).updateDocuments([document], { primaryKey: 'id' })
}

// Function to add documents with vectors
export async function addDocuments(indexUid: string, documents: Record<string, any>[]) {
  // Generate embeddings for each document's question
  const documentsWithVectors = await Promise.all(
    documents.map(async (doc) => {
      if (!doc.vector && doc.question) {
        const vector = await generateEmbedding(doc.question)
        return {
          ...doc,
          _vectors: {
            default: {
              embeddings: vector,
              regenerate: false
            }
          }
        }
      }
      return doc
    })
  )

  return await client.index(indexUid).addDocuments(documentsWithVectors, { primaryKey: 'id' })
}

// Basic search function
export async function basicSearch(indexUid: string, query: string, searchParams?: SearchParams) {
  const params: SearchParams = {
    ...searchParams,
    limit: 5, // Adjust the limit as needed
    showRankingScore: true,
    attributesToSearchOn: ['question'],
    matchingStrategy: 'all',
    showRankingScoreDetails: true
  }
  return await client.index(indexUid).search(query, params)
}

export const getSearchFilters = (groups: string[], chatFlows?: string[], userIds?: string[]) => {
  const filters: string[] = []

  const groupFilters = groups.length ? `allowedGroups IN [${groups.join(', ')}]` : ''
  const userIdFilters = userIds?.length ? `allowedUserIds IN [${userIds.join(', ')}]` : ''
  const chatFlowFilters = chatFlows?.length ? `allowedChatFlows IN [${chatFlows.join(', ')}]` : ''

  const groupOrUser = [groupFilters, userIdFilters].filter(Boolean).join(' OR ')
  if (groupOrUser) filters.push(`(${groupOrUser})`)
  if (chatFlowFilters) filters.push(chatFlowFilters)

  return filters.join(' AND ')
}

export const formatFilesByChatFlows = (files: any) => {
  const filtered = files.filter((file: any) => Array.isArray(file.allowedChatFlows))
  const grouped: any = {}

  filtered.forEach((item: any) => {
    item.scope = !item.visibility || item.visibility === 'public' ? 'Chung' : Array.isArray(item.allowedGroups) ? 'Nhom' : 'Ca nhan'
    item.allowedChatFlows.forEach((flow: any) => {
      if (!grouped[flow]) {
        grouped[flow] = []
      }
      grouped[flow].push(item)
    })
  })

  return grouped
}

const indexName = `documents_${process.env.S3_STORAGE_ACCESS_KEY_ID || process.env.AWS_ACCESS_KEY_ID}`
export const basicSearchByFilters = async (
  query: string,
  rootPrefix: string,
  limit: number,
  offset: number,
  groups: string[],
  chatFlows?: string[],
  userIds?: string[]
) => {
  try {
    const filter = getSearchFilters(groups, chatFlows, userIds)
    console.log('🚀 ~ meilisearch.ts:171 ~ filter:', filter)

    const index = client.index(indexName)
    let result
    const response = await index.search(rootPrefix + query, {
      q: rootPrefix + query,
      limit,
      offset,
      filter
    })
    if (response.hits.length <= 0) {
      return []
    } else {
      const hits = response.hits.map((doc) => ({
        key: doc.key,
        name: doc.name,
        size: doc.size,
        etag: doc.etag,
        lastModified: doc.lastModified,
        idFolder: doc.idFolder,
        userId: doc.userId,
        visibility: doc.visibility,
        allowedGroups: doc.allowedGroups,
        allowedChatFlows: doc.allowedChatFlows,
        allowedUserIds: doc.allowedUserIds
      }))
      result = hits
    }
    return { result, estimatedTotalHits: response.estimatedTotalHits }
  } catch (error) {
    console.error('Error performing basic search:', error)
  }
}

export const basicSearchAll = async (
  query: string,
  rootPrefix: string,
  limit: number = 200,
  offset: number = 0,
  groups: string[],
  chatFlows?: string[],
  userIds?: string[]
) => {
  try {
    let allResults: any[] = []
    let hasMore = true
    while (hasMore) {
      const searchResponse: any = await basicSearchByFilters(query, rootPrefix, limit, offset, groups, chatFlows, userIds)
      const result = searchResponse?.result || []
      allResults = [...allResults, ...result]

      if (result.length < limit) {
        hasMore = false
      } else {
        offset += limit
      }
    }

    return allResults
  } catch (error) {
    console.error('Error performing basic search all:', error)
    return []
  }
}

// Function for vector search
export async function vectorSearch(indexUid: string, query: string, searchParams?: SearchParams) {
  if (query?.length < 5) {
    return null
  }

  const queryVector = await generateEmbedding(query)

  return await client.index(indexUid).search(query, {
    ...searchParams,
    vector: queryVector,
    hybrid: {
      embedder: 'default'
    },
    limit: 1,
    showRankingScore: true,
    attributesToSearchOn: ['question'],
    matchingStrategy: 'all',
    showRankingScoreDetails: true,
    rankingScoreThreshold: 0.9
  })
}

// Hybrid search combining keyword and vector search results
export async function hybridSearch(indexUid: string, query: string, searchParams?: SearchParams) {
  try {
    // Generate embedding for the query
    const queryVector = await generateEmbedding(query)

    // Use MeiliSearch's hybrid search
    const results = await client.index(indexUid).search('', {
      ...searchParams,
      vector: queryVector, // The generated vector for the query
      hybrid: {
        embedder: 'default',
        semanticRatio: 0.5
      },
      limit: searchParams?.limit || 5,
      showRankingScore: true,
      attributesToSearchOn: ['question'],
      matchingStrategy: 'all',
      showRankingScoreDetails: true
    })

    return results
  } catch (error) {
    console.error('Error in hybrid search:', error)
    // Fallback to basic search if there's an error
    return basicSearch(indexUid, query, searchParams)
  }
}

// Function to update index settings
export async function updateSettings(indexUid: string, settings: Settings) {
  return await client.index(indexUid).updateSettings(settings)
}

// Function to get all documents from an index with pagination
export async function getAllDocuments(indexUid: string, limit: number = 20, offset: number = 0) {
  return await client.index(indexUid).getDocuments({
    limit,
    offset
  })
}

// Function to get a document by ID
export async function getDocumentById(indexUid: string, documentId: string) {
  return await client.index(indexUid).getDocument(documentId)
}

// Function to add default embedder setting for an index
export async function addUserProvidedEmbedderSetting(indexUid: string) {
  try {
    // Amazon Titan embedding model has 1536 dimensions
    const settings: Settings = {
      embedders: {
        default: {
          source: 'userProvided',
          dimensions: 1024
        }
      }
    }
    return await updateSettings(indexUid, settings)
  } catch (error) {
    console.error('Error adding embedder settings:', error)
    throw error
  }
}

const desiredFilterableAttributes = ['userId', 'visibility', 'allowedGroups', 'allowedChatFlows']
export const setupMeilisearch = async () => {
  try {
    const index = client.index(indexName)
    const currentAttributes = await index.getFilterableAttributes()

    const sortedCurrent = [...currentAttributes].sort()
    const sortedDesired = [...desiredFilterableAttributes].sort()

    const needUpdate = JSON.stringify(sortedCurrent) !== JSON.stringify(sortedDesired)
    if (needUpdate) {
      await index.updateFilterableAttributes(desiredFilterableAttributes)
    }
  } catch (error) {
    console.error('Error setup meilisearch:', error)
  }
}

export const updateDocumentUser = async (key: string, newDocument: any) => {
  try {
    const index = client.index(indexName)
    const searchResponse = await index.search(key, {
      rankingScoreThreshold: 0.9,
      q: key,
      limit: 1
    })

    if (searchResponse?.hits.length <= 0) {
      return
    }
    const document = searchResponse?.hits.filter((doc) => doc.key === key)[0]

    const mergeObjects = (obj1: Record<string, any>, obj2: Record<string, any>): Record<string, any> => {
      return Object?.keys(obj1).reduce((acc, key) => {
        if (Object.prototype.hasOwnProperty.call(obj2, key) && obj2[key]?.trim?.()) {
          acc[key] = obj2[key]
        } else {
          acc[key] = obj1[key]
        }
        return acc
      }, {} as Record<string, any>)
    }

    const result: any = mergeObjects(document, newDocument)

    newDocument?.visibility?.trim?.() && (result.visibility = newDocument.visibility)
    newDocument?.allowedGroups && (result.allowedGroups = newDocument.allowedGroups)
    newDocument?.allowedChatFlows && (result.allowedChatFlows = newDocument.allowedChatFlows)

    const response = await index.updateDocuments(result, { primaryKey: 'id' })
    return response
  } catch (error) {
    console.error('Error updating document:', error)
  }
}
