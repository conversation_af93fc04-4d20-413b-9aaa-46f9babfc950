import { closeSnackbar as closeSnackbarAction, enqueueSnackbar as enqueueSnackbarAction, REMOVE_DIRTY } from '@/store/actions'
import { exportData, stringify } from '@/utils/exportImport'
import useNotifier from '@/utils/useNotifier'
import PropTypes from 'prop-types'
import { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { createPortal } from 'react-dom'

// material-ui
import {
  Avatar,
  Box,
  Button,
  ButtonBase,
  Checkbox,
  ClickAwayListener,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControlLabel,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Paper,
  Popper,
  Stack,
  styled,
  Typography
} from '@mui/material'
import { useTheme } from '@mui/material/styles'

// project imports
import AboutDialog from '@/ui-component/dialog/AboutDialog'
import Transitions from '@/ui-component/extended/Transitions'

// assets
import { IconFileExport, IconFileUpload, IconLogout, IconSettings, IconX, IconClipboardList } from '@tabler/icons-react'
import './index.css'
import ExportingGIF from '@/assets/images/Exporting.gif'

//API
import exportImportApi from '@/api/exportimport'

// Hooks
import useApi from '@/hooks/useApi'
import { getErrorMessage } from '@/utils/errorHandler'
import { useNavigate } from 'react-router-dom'
import { Link } from 'react-router-dom'
import { IconUsers } from '@tabler/icons-react'
import { IconDatabase } from '@tabler/icons-react'

const dataToExport = ['Chatflows', 'Agentflows', 'Tools', 'Variables', 'Assistants']

const ExportDialog = ({ show, onCancel, onExport }) => {
  const portalElement = document.getElementById('portal')
  const [selectedData, setSelectedData] = useState(['Chatflows', 'Agentflows', 'Tools', 'Variables', 'Assistants'])
  const [isExporting, setIsExporting] = useState(false)

  useEffect(() => {
    if (show) setIsExporting(false)

    return () => {
      setIsExporting(false)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show])

  const component = show ? (
    <Dialog
      onClose={!isExporting ? onCancel : undefined}
      open={show}
      fullWidth
      maxWidth='sm'
      aria-labelledby='export-dialog-title'
      aria-describedby='export-dialog-description'
    >
      <DialogTitle sx={{ fontSize: '1rem' }} id='export-dialog-title'>
        {!isExporting ? 'Chọn dữ liệu' : 'Đang xuất dữ liệu..'}
      </DialogTitle>
      <DialogContent>
        {!isExporting && (
          <Stack direction='row' sx={{ gap: 1, flexWrap: 'wrap' }}>
            {dataToExport.map((data, index) => (
              <FormControlLabel
                key={index}
                size='small'
                control={
                  <Checkbox
                    color='success'
                    checked={selectedData.includes(data)}
                    onChange={(event) => {
                      setSelectedData(event.target.checked ? [...selectedData, data] : selectedData.filter((item) => item !== data))
                    }}
                  />
                }
                label={data}
              />
            ))}
          </Stack>
        )}
        {isExporting && (
          <Box sx={{ height: 'auto', display: 'flex', justifyContent: 'center', mb: 3 }}>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <img
                style={{
                  objectFit: 'cover',
                  height: 'auto',
                  width: 'auto'
                }}
                src={ExportingGIF}
                alt='ExportingGIF'
              />
              <span>Exporting data might takes a while</span>
            </div>
          </Box>
        )}
      </DialogContent>
      {!isExporting && (
        <DialogActions>
          <Button onClick={onCancel}>Đóng</Button>
          <Button
            disabled={selectedData.length === 0}
            variant='contained'
            onClick={() => {
              setIsExporting(true)
              onExport(selectedData)
            }}
          >
            Xuất dữ liệu
          </Button>
        </DialogActions>
      )}
    </Dialog>
  ) : null

  return createPortal(component, portalElement)
}

ExportDialog.propTypes = {
  show: PropTypes.bool,
  onCancel: PropTypes.func,
  onExport: PropTypes.func
}

// ==============================|| PROFILE MENU ||============================== //

const ProfileSection = ({ username, handleLogout }) => {
  const user = useSelector((state) => state.user)
  const isUser = user?.role === 'USER'
  const theme = useTheme()

  const [open, setOpen] = useState(false)
  const [aboutDialogOpen, setAboutDialogOpen] = useState(false)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)

  const anchorRef = useRef(null)
  const inputRef = useRef()

  const navigate = useNavigate()

  const importAllApi = useApi(exportImportApi.importData)
  const exportAllApi = useApi(exportImportApi.exportData)
  const prevOpen = useRef(open)

  // ==============================|| Snackbar ||============================== //

  useNotifier()
  const dispatch = useDispatch()
  const enqueueSnackbar = (...args) => dispatch(enqueueSnackbarAction(...args))
  const closeSnackbar = (...args) => dispatch(closeSnackbarAction(...args))

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return
    }
    setOpen(false)
  }

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
  }

  const errorFailed = (message) => {
    enqueueSnackbar({
      message: message,
      options: {
        key: new Date().getTime() + Math.random(),
        variant: 'error',
        persist: true,
        action: (key) => (
          <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
            <IconX />
          </Button>
        )
      }
    })
  }

  const fileChange = (e) => {
    if (!e.target.files) return

    const file = e.target.files[0]

    const reader = new FileReader()
    reader.onload = (evt) => {
      if (!evt?.target?.result) {
        return
      }
      const body = JSON.parse(evt.target.result)
      importAllApi.request(body)
    }
    reader.readAsText(file)
  }

  const importAllSuccess = () => {
    dispatch({ type: REMOVE_DIRTY })
    enqueueSnackbar({
      message: `Nhúng thành công`,
      options: {
        key: new Date().getTime() + Math.random(),
        variant: 'success',
        action: (key) => (
          <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
            <IconX />
          </Button>
        )
      }
    })
  }

  const importAll = () => {
    inputRef.current.click()
  }

  const onExport = (data) => {
    const body = {}
    if (data.includes('Chatflows')) body.chatflow = true
    if (data.includes('Agentflows')) body.agentflow = true
    if (data.includes('Tools')) body.tool = true
    if (data.includes('Variables')) body.variable = true
    if (data.includes('Assistants')) body.assistant = true

    exportAllApi.request(body)
  }

  const renderTextBio = () => {
    switch (user.role) {
      case 'MASTER_ADMIN':
        return 'Bạn là master-admin'
      case 'SITE_ADMIN':
        return `Bạn là site admin nhóm ${user.groupname}`
      case 'ADMIN':
        return `Bạn là admin nhóm ${user.groupname}`
      default:
        return `Bạn là user nhóm ${user.groupname}`
    }
  }

  useEffect(() => {
    if (importAllApi.data) {
      importAllSuccess()
      navigate(0)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [importAllApi.data])

  useEffect(() => {
    if (importAllApi.error) {
      let errMsg = 'Invalid Imported File'
      let error = importAllApi.error
      if (error?.response?.data) {
        errMsg = typeof error.response.data === 'object' ? error.response.data.message : error.response.data
      }
      errorFailed(`Failed to import: ${errMsg}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [importAllApi.error])

  useEffect(() => {
    if (exportAllApi.data) {
      setExportDialogOpen(false)
      try {
        const dataStr = stringify(exportData(exportAllApi.data))
        //const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr)
        const blob = new Blob([dataStr], { type: 'application/json' })
        const dataUri = URL.createObjectURL(blob)

        const linkElement = document.createElement('a')
        linkElement.setAttribute('href', dataUri)
        linkElement.setAttribute('download', exportAllApi.data.FileDefaultName)
        linkElement.click()
      } catch (error) {
        errorFailed(`Failed to export all: ${getErrorMessage(error)}`)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exportAllApi.data])

  useEffect(() => {
    if (exportAllApi.error) {
      setExportDialogOpen(false)
      let errMsg = 'Internal Server Error'
      let error = exportAllApi.error
      if (error?.response?.data) {
        errMsg = typeof error.response.data === 'object' ? error.response.data.message : error.response.data
      }
      errorFailed(`Failed to export: ${errMsg}`)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exportAllApi.error])

  useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current.focus()
    }

    prevOpen.current = open
  }, [open])

  const StyledListItem = styled(ListItemButton)(({ theme }) => ({
    borderRadius: theme.shape.borderRadius,
    transition: 'all 0.3s',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
      transform: 'translateY(-2px)'
    }
  }))

  return (
    <>
      <ButtonBase ref={anchorRef} sx={{ borderRadius: '12px', overflow: 'hidden' }}>
        <Avatar
          variant='rounded'
          sx={{
            ...theme.typography.commonAvatar,
            ...theme.typography.mediumAvatar,
            transition: 'all .2s ease-in-out',
            background: theme.palette.secondary.light,
            color: theme.palette.secondary.dark,
            '&:hover': {
              background: theme.palette.secondary.dark,
              color: theme.palette.secondary.light
            }
          }}
          onClick={handleToggle}
          color='inherit'
        >
          <IconSettings stroke={1.5} size='1.3rem' />
        </Avatar>
      </ButtonBase>
      <Popper
        placement='bottom-end'
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, 14]
              }
            }
          ]
        }}
      >
        {({ TransitionProps }) => (
          <Transitions in={open} {...TransitionProps}>
            <Paper elevation={8} sx={{ borderRadius: 3, overflow: 'hidden', minWidth: 250 }}>
              <ClickAwayListener onClickAway={handleClose}>
                <Box sx={{ p: 2 }}>
                  {username && (
                    <Typography variant='h5' fontWeight={600} gutterBottom>
                      Tên: {username}
                    </Typography>
                  )}
                  <Typography variant='body2' color='text.secondary'>
                    {renderTextBio()}
                  </Typography>
                  <Divider sx={{ my: 2 }} />
                  <List>
                    {!isUser && (
                      <StyledListItem component={Link} to='/admin-account'>
                        <ListItemIcon>
                          <IconUsers size='1.3rem' />
                        </ListItemIcon>
                        <ListItemText primary='Tài khoản admin' />
                      </StyledListItem>
                    )}
                    <StyledListItem component={Link} to={`/profile/${user?.id}`}>
                      <ListItemIcon>
                        <IconDatabase size='1.3rem' />
                      </ListItemIcon>
                      <ListItemText primary='Quản lý dữ liệu cá nhân' />
                    </StyledListItem>
                    <StyledListItem onClick={() => setExportDialogOpen(true)}>
                      <ListItemIcon>
                        <IconFileExport size='1.3rem' />
                      </ListItemIcon>
                      <ListItemText primary='Xuất dữ liệu' />
                    </StyledListItem>
                    <StyledListItem onClick={importAll}>
                      <ListItemIcon>
                        <IconFileUpload size='1.3rem' />
                      </ListItemIcon>
                      <ListItemText primary='Nhúng dữ liệu' />
                    </StyledListItem>
                    <StyledListItem component={Link} to='/logs'>
                      <ListItemIcon>
                        <IconClipboardList size='1.3rem' />
                      </ListItemIcon>
                      <ListItemText primary='Lịch sử log' />
                    </StyledListItem>
                    <input ref={inputRef} type='file' hidden onChange={fileChange} accept='.json' />
                    {username && (
                      <StyledListItem onClick={handleLogout}>
                        <ListItemIcon>
                          <IconLogout size='1.3rem' />
                        </ListItemIcon>
                        <ListItemText primary='Đăng xuất' />
                      </StyledListItem>
                    )}
                  </List>
                </Box>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
      <AboutDialog show={aboutDialogOpen} onCancel={() => setAboutDialogOpen(false)} />
      <ExportDialog show={exportDialogOpen} onCancel={() => setExportDialogOpen(false)} onExport={(data) => onExport(data)} />
    </>
  )
}

ProfileSection.propTypes = {
  username: PropTypes.string,
  handleLogout: PropTypes.func
}

export default ProfileSection
