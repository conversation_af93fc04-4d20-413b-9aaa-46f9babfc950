import { z } from 'zod'
import { MCPServer } from '../types'
import articleService from '../../services/articles'
import dayjs from 'dayjs'

export const GetCrawlDataMCPServer: MCPServer<any> = {
  name: 'get-crawl-data',
  description: 'L<PERSON>y thông tin crawl data trong ngày',
  params: {
    category: z.string().optional().default('chung-khoan')
  },
  cb: async ({ category = 'chung-khoan' }: any) => {
    try {
      const repsonse = await articleService.getAllArticles(category, dayjs(Date.now()))

      if (repsonse)
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(repsonse)
            }
          ]
        }
    } catch (err) {
      console.error('Error in Get crawl data MCP', err)
      return {
        content: [{ type: 'text', text: 'error' }]
      }
    } finally {
      return {
        content: [{ type: 'text', text: 'No response found' }]
      }
    }
  }
}
